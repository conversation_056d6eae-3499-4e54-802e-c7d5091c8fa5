import requests
from bs4 import BeautifulSoup
import json
import time
import csv
from urllib.parse import urljoin, urlparse
import re

class GumtreeScraper:
    def __init__(self):
        self.base_url = "https://www.gumtree.co.za"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def search_student_accommodation(self, location="cape-town", max_pages=5):
        """Search for student accommodation listings"""
        listings = []
        
        for page in range(1, max_pages + 1):
            search_url = f"{self.base_url}/s-flatshare-houseshare/{location}/student/page-{page}/v1c9073l3100006a{page}p1"
            
            try:
                response = self.session.get(search_url)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.content, 'html.parser')
                page_listings = self.extract_listings(soup)
                listings.extend(page_listings)
                
                print(f"Scraped page {page}: {len(page_listings)} listings")
                time.sleep(2)  # Be respectful
                
            except Exception as e:
                print(f"Error scraping page {page}: {e}")
                
        return listings
    
    def extract_listings(self, soup):
        """Extract listing data from search results page"""
        listings = []
        
        # Find listing containers
        listing_containers = soup.find_all('div', class_='user-ad-row')
        
        for container in listing_containers:
            try:
                listing = self.extract_listing_details(container)
                if listing:
                    listings.append(listing)
            except Exception as e:
                print(f"Error extracting listing: {e}")
                
        return listings
    
    def extract_listing_details(self, container):
        """Extract details from a single listing container"""
        listing = {}
        
        # Title
        title_elem = container.find('a', class_='related-ad-title')
        if title_elem:
            listing['title'] = title_elem.get_text(strip=True)
            listing['url'] = urljoin(self.base_url, title_elem.get('href'))
        
        # Price
        price_elem = container.find('div', class_='user-ad-price')
        if price_elem:
            listing['price'] = price_elem.get_text(strip=True)
        
        # Location
        location_elem = container.find('div', class_='user-ad-location')
        if location_elem:
            listing['location'] = location_elem.get_text(strip=True)
        
        # Description preview
        desc_elem = container.find('div', class_='user-ad-description')
        if desc_elem:
            listing['description_preview'] = desc_elem.get_text(strip=True)
        
        # Image
        img_elem = container.find('img')
        if img_elem and img_elem.get('src'):
            listing['image_url'] = img_elem.get('src')
        
        # Get full details if we have a URL
        if listing.get('url'):
            full_details = self.get_full_listing_details(listing['url'])
            listing.update(full_details)
        
        return listing
    
    def get_full_listing_details(self, listing_url):
        """Get full details from individual listing page"""
        details = {}
        
        try:
            response = self.session.get(listing_url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Full description
            desc_elem = soup.find('div', class_='ad-description')
            if desc_elem:
                details['description'] = desc_elem.get_text(strip=True)
            
            # Contact information
            contact_elem = soup.find('div', class_='seller-contact-options')
            if contact_elem:
                # Phone number
                phone_elem = contact_elem.find('span', class_='phone-number')
                if phone_elem:
                    details['phone'] = phone_elem.get_text(strip=True)
                
                # Email link
                email_elem = contact_elem.find('a', href=re.compile(r'mailto:'))
                if email_elem:
                    details['email_link'] = email_elem.get('href')
            
            # Additional images
            image_gallery = soup.find('div', class_='vip-gallery')
            if image_gallery:
                images = []
                for img in image_gallery.find_all('img'):
                    if img.get('src'):
                        images.append(img.get('src'))
                details['all_images'] = images
            
            time.sleep(1)  # Be respectful
            
        except Exception as e:
            print(f"Error getting full details for {listing_url}: {e}")
            
        return details
    
    def save_to_csv(self, listings, filename="gumtree_listings.csv"):
        """Save listings to CSV file"""
        if not listings:
            print("No listings to save")
            return
        
        fieldnames = ['title', 'price', 'location', 'description', 'description_preview', 
                     'image_url', 'all_images', 'phone', 'email_link', 'url']
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for listing in listings:
                # Convert list to string for CSV
                if 'all_images' in listing and isinstance(listing['all_images'], list):
                    listing['all_images'] = '; '.join(listing['all_images'])
                writer.writerow(listing)
        
        print(f"Saved {len(listings)} listings to {filename}")
    
    def save_to_json(self, listings, filename="gumtree_listings.json"):
        """Save listings to JSON file"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(listings, f, indent=2, ensure_ascii=False)
        
        print(f"Saved {len(listings)} listings to {filename}")

def main():
    scraper = GumtreeScraper()
    
    # Search for student accommodation in Cape Town area
    print("Scraping Gumtree student accommodation listings...")
    listings = scraper.search_student_accommodation(location="cape-town", max_pages=3)
    
    if listings:
        print(f"\nFound {len(listings)} listings")
        
        # Save results
        scraper.save_to_csv(listings)
        scraper.save_to_json(listings)
        
        # Print sample listing
        print("\nSample listing:")
        print(json.dumps(listings[0], indent=2))
    else:
        print("No listings found")

if __name__ == "__main__":
    main()
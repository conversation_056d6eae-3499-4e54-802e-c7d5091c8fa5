from gumtree_scraper import GumtreeScraper
import json

def scrape_specific_areas():
    """Scrape specific student areas like Hatfield, UCT area"""
    scraper = GumtreeScraper()
    all_listings = []
    
    # Target areas
    search_terms = [
        ("cape-town", "uct"),
        ("cape-town", "rondebosch"), 
        ("cape-town", "observatory"),
        ("pretoria", "hatfield"),
        ("johannesburg", "wits")
    ]
    
    for location, area in search_terms:
        print(f"\nScraping {area} in {location}...")
        
        # Modify search URL for specific areas
        search_url = f"https://www.gumtree.co.za/s-flatshare-houseshare/{location}/{area}+student/v1c9073l3100006"
        
        try:
            response = scraper.session.get(search_url)
            soup = BeautifulSoup(response.content, 'html.parser')
            listings = scraper.extract_listings(soup)
            
            # Add area info to each listing
            for listing in listings:
                listing['search_area'] = f"{area}, {location}"
            
            all_listings.extend(listings)
            print(f"Found {len(listings)} listings in {area}")
            
        except Exception as e:
            print(f"Error scraping {area}: {e}")
    
    return all_listings

if __name__ == "__main__":
    listings = scrape_specific_areas()
    
    if listings:
        scraper = GumtreeScraper()
        scraper.save_to_csv(listings, "student_areas_listings.csv")
        scraper.save_to_json(listings, "student_areas_listings.json")